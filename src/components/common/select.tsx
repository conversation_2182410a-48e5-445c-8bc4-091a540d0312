import { faChevronDown, faChevronUp } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { cn } from "@utils/cn";
import { useState } from "react";

interface SelectOption {
  value: string;
  label: string;
}

interface SelectProps
  extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, "size"> {
  className?: string;
  options: SelectOption[];
  placeholder?: string;
  variant?: "primary" | "secondary" | "accent";
  size?: "sm" | "md" | "lg";
}

export const Select = ({
  className,
  options,
  placeholder = "Select an option",
  variant = "primary",
  size = "md",
  ...props
}: SelectProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const sizeClasses = {
    lg: "h-10 text-sm",
    md: "h-9 text-body-xs",
    sm: "h-8 text-xs",
  };

  const variantClasses = {
    accent: "select-accent",
    primary: "select-primary",
    secondary: "select-secondary",
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="dropdown dropdown-start w-full">
      <button
        type="button"
        onClick={toggleDropdown}
        className={cn(
          "flex w-full appearance-none items-center justify-between rounded-lg border-none bg-base-200 px-4",
          "focus:outline-none focus:ring-2 focus:ring-primary",
          sizeClasses[size],
          variantClasses[variant],
          className
        )}
      >
        {placeholder}
        <FontAwesomeIcon
          icon={faChevronUp}
          className={cn(
            "select-icon",
            { "rotate-180": isOpen },
            "transition-transform duration-200"
          )}
        />
      </button>
      {isOpen && (
        <div className="dropdown-content menu z-1 w-52 rounded-box bg-base-100 p-2 shadow-sm">
          {options.map((option) => (
            <button
              key={option.value}
              type="button"
              className="w-full rounded-lg px-4 py-2 hover:bg-primary/10"
            >
              {option.label}
            </button>
          ))}
        </div>
      )}
      {/* <select
        {...props}
        className={cn(
          "select w-full appearance-none rounded-lg border-none bg-base-200 pr-8",
          "focus:outline-none focus:ring-2 focus:ring-primary",
          sizeClasses[size],
          variantClasses[variant],
          className,
        )}
      > */}
      {/* {placeholder && (
          <option value="" disabled>
            {placeholder}
          </option>
        )}
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      <FontAwesomeIcon
        icon={faChevronDown}
        className="-translate-y-1/2 pointer-events-none absolute top-1/2 right-3 text-neutral text-xs"
      /> */}
    </div>
  );
};
